import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';

class CreateBillingController extends GetxController {
  final ApiService _apiService = ApiService();
  final BillingScreenMode mode;
  final BillingData? initialBillingData;

  CreateBillingController({this.mode = BillingScreenMode.create, this.initialBillingData});

  // Observables for patient search
  var searchPatientController = TextEditingController();
  var searchQuery = ''.obs;
  var searchResults = <PatientData>[].obs;
  var isSearching = false.obs;
  var selectedPatient = Rxn<PatientData>();

  // Observables for service search
  var serviceSearchQuery = ''.obs;
  var serviceSearchResults = <ServiceModel>[].obs;
  var isServiceSearching = false.obs;
  var selectedServices = <BillingService>[].obs;
  var allServices = <ServiceModel>[].obs;
  var selectedPaymentSource = RxnString();

  // Form field controllers
  late TextEditingController firstNameController;
  late TextEditingController lastNameController;
  late TextEditingController patientIdController;
  late TextEditingController ageController;
  late TextEditingController dobController;
  late TextEditingController searchServiceController;
  late TextEditingController priceController;
  late TextEditingController discountController;
  late TextEditingController discountAmountController;
  late TextEditingController totalAmountController;
  late TextEditingController paidAmountController;
  late TextEditingController balanceAmountController;

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers
    firstNameController = TextEditingController();
    lastNameController = TextEditingController();
    patientIdController = TextEditingController();
    ageController = TextEditingController();
    dobController = TextEditingController();
    searchServiceController = TextEditingController();
    priceController = TextEditingController();
    discountController = TextEditingController();
    discountAmountController = TextEditingController();
    totalAmountController = TextEditingController();
    paidAmountController = TextEditingController();
    balanceAmountController = TextEditingController();

    if ((mode == BillingScreenMode.edit || mode == BillingScreenMode.view) && initialBillingData != null) {
      fetchBillingDetails(initialBillingData!.id!);
    } else if (initialBillingData != null) {
      populateInitialData(initialBillingData!);
    }

    // Debounce search queries
    debounce(searchQuery, (query) {
      if (query.length > 1) { // Changed from 2 to 1 for 2+ characters
        searchPatients(query);
      } else {
        searchResults.clear();
      }
    }, time: const Duration(milliseconds: 500));

    // Debounce service search queries
    debounce(serviceSearchQuery, (query) {
      if (query.length > 1) { // 2+ characters for service search
        searchServices(query);
      } else {
        serviceSearchResults.clear();
      }
    }, time: const Duration(milliseconds: 300));

    // Load all services on init
    loadAllServices();

    // Add listeners for automatic calculation
    discountController.addListener(() {
      if (discountController.text.isNotEmpty) {
        discountAmountController.clear();
      }
      calculateTotal();
    });

    discountAmountController.addListener(() {
      if (discountAmountController.text.isNotEmpty) {
        discountController.clear();
      }
      calculateTotal();
    });

    paidAmountController.addListener(updateBalance);
  }

  @override
  void onClose() {
    // Dispose all controllers
    searchPatientController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    patientIdController.dispose();
    ageController.dispose();
    dobController.dispose();
    searchServiceController.dispose();
    priceController.dispose();
    discountController.dispose();
    discountAmountController.dispose();
    totalAmountController.dispose();
    paidAmountController.dispose();
    balanceAmountController.dispose();
    super.onClose();
  }

  Future<void> searchPatients(String query) async {
    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      searchResults.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
    } finally {
      isSearching.value = false;
    }
  }

  void selectPatient(PatientData patient) {
    selectedPatient.value = patient;
    searchPatientController.text = patient.displayName;

    // Populate form fields
    firstNameController.text = patient.firstName ?? '';
    lastNameController.text = patient.lastName ?? '';
    patientIdController.text = patient.patientRegId ?? '';
    ageController.text = patient.age?.toString() ?? '';
    dobController.text = patient.dob ?? '';

    searchResults.clear();
  }

  Future<void> loadAllServices() async {
    try {
      final response = await _apiService.getAllServices();
      allServices.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load services: $e');
      allServices.value = []; // Initialize with empty list on error
    }
  }

  void searchServices(String query) {
    if (allServices.isEmpty) {
      loadAllServices();
      return;
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    serviceSearchResults.value = filteredServices;
  }

  void selectService(ServiceModel service) {
    // Check if the service is already selected
    final existingServiceIndex = selectedServices.indexWhere((s) => s.serviceId == service.id);

    if (existingServiceIndex != -1) {
      // If service already exists, increment its quantity
      incrementQuantity(selectedServices[existingServiceIndex]);
    } else {
      // Otherwise, add the new service with quantity 1
      selectedServices.add(BillingService(
        serviceId: service.id,
        serviceName: service.serviceName,
        price: service.price,
        quantity: 1,
      ));
    }

    calculateTotal();
    searchServiceController.clear();
    serviceSearchResults.clear();
  }

  void incrementQuantity(BillingService service) {
    final index = selectedServices.indexOf(service);
    if (index != -1) {
      var updatedService = BillingService(
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        price: service.price,
        quantity: service.quantity + 1,
      );
      selectedServices[index] = updatedService;
      calculateTotal();
    }
  }

  void decrementQuantity(BillingService service) {
    final index = selectedServices.indexOf(service);
    if (index != -1 && service.quantity > 1) {
      var updatedService = BillingService(
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        price: service.price,
        quantity: service.quantity - 1,
      );
      selectedServices[index] = updatedService;
      calculateTotal();
    }
  }

  void removeService(BillingService service) {
    selectedServices.remove(service);
    calculateTotal();
  }

  void calculateTotal() {
    double total = 0;
    for (var service in selectedServices) {
      total += service.price * service.quantity;
    }

    // Apply discount
    final discountPercent = double.tryParse(discountController.text) ?? 0;
    final discountAmount = double.tryParse(discountAmountController.text) ?? 0;

    if (discountPercent > 0) {
      total -= total * (discountPercent / 100);
    } else if (discountAmount > 0) {
      total -= discountAmount;
    }

    totalAmountController.text = total.toStringAsFixed(2);
    updateBalance();
  }

  void updateBalance() {
    final total = double.tryParse(totalAmountController.text) ?? 0;
    final paid = double.tryParse(paidAmountController.text) ?? 0;
    final balance = total - paid;
    balanceAmountController.text = balance.toStringAsFixed(2);
  }

  Future<void> fetchBillingDetails(String billingId) async {
    try {
      final response = await _apiService.getBillingById(billingId);
      if (response.status) {
        populateInitialData(response.data!);
      } else {
        Get.snackbar('Error', response.message ?? 'Failed to fetch billing details.');
      }
    } catch (e) {
      print('Error fetching billing details: $e');
      Get.snackbar('Error', 'An error occurred: $e');
    }
  }

  // Method to return filtered patients for CustomSearchField
  Future<List<PatientData>> getFilteredPatients(String query) async {
    if (query.length <= 1) return [];

    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      return response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
      return [];
    } finally {
      isSearching.value = false;
    }
  }

  // Method to return filtered services for CustomSearchField
  Future<List<ServiceModel>> getFilteredServices(String query) async {
    if (query.length <= 1) return [];

    if (allServices.isEmpty) {
      await loadAllServices();
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    return filteredServices;
  }

  void populateInitialData(BillingData data) {
    firstNameController.text = data.firstName ?? '';
    lastNameController.text = data.lastName ?? '';
    patientIdController.text = data.patientRegId ?? '';
    ageController.text = data.age?.toString() ?? '';
    selectedPatient.value = PatientData(
      firstName: data.firstName,
      lastName: data.lastName,
      patientRegId: data.patientRegId,
      age: data.age?.toString(),
      gender: data.gender,
      mobile: data.mobile,
    );
    selectedServices.value = data.services;
    discountController.text = data.discountPercent ?? '';
    discountAmountController.text = data.discountAmount ?? '';
    totalAmountController.text = data.totalAmount ?? '';
    paidAmountController.text = data.paidAmount ?? '';
    balanceAmountController.text = data.balanceAmount ?? '';
    selectedPaymentSource.value = data.paymentSource;
  }

  Future<void> saveBilling() async {
    if (selectedPatient.value == null) {
      Get.snackbar('Error', 'Please select a patient.');
      return;
    }
    if (selectedServices.isEmpty) {
      Get.snackbar('Error', 'Please select at least one service.');
      return;
    }
    if (paidAmountController.text.isEmpty) {
      Get.snackbar('Error', 'Please enter the paid amount.');
      return;
    }
    if (selectedPaymentSource.value == null) {
      Get.snackbar('Error', 'Please select a payment source.');
      return;
    }

    final request = BillingUpsertRequest(
      id: mode == BillingScreenMode.edit ? initialBillingData!.id : null,
      firstName: firstNameController.text,
      lastName: lastNameController.text,
      patientRegId: patientIdController.text,
      gender: selectedPatient.value!.gender!,
      age: int.parse(ageController.text),
      services: selectedServices.map((service) => BillingServiceRequest(
        serviceId: service.serviceId!,
        serviceName: service.serviceName!,
        quantity: service.quantity,
        price: service.price,
      )).toList(),
      discountPercent: discountController.text,
      discountAmount: discountAmountController.text,
      totalAmount: totalAmountController.text,
      paidAmount: paidAmountController.text,
      balanceAmount: balanceAmountController.text,
      paymentSource: selectedPaymentSource.value!,
      mobile: selectedPatient.value!.mobile!,
    );

    try {
      final response = await _apiService.upsertBilling(request);
      if (response.status) {
        Get.snackbar('Success', 'Billing information saved successfully.');
        Get.back(); // Go back to the previous screen
      } else {
        Get.snackbar('Error', response.message ?? 'Failed to save billing information.');
      }
    } catch (e) {
      Get.snackbar('Error', 'An error occurred: $e');
    }
  }
}
