import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/create_billing_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/billing_model.dart';

enum BillingScreenMode { create, edit, view }

class CreateBillingScreen extends StatelessWidget {
  final bool showSearchOption;
  final BillingScreenMode mode;
  final String? patientName;
  final String? serviceName;
  final String? amount;
  final String? date;
  final BillingData? billingData;

  const CreateBillingScreen({
    super.key,
    this.showSearchOption = true,
    this.mode = BillingScreenMode.create,
    this.patientName,
    this.serviceName,
    this.amount,
    this.date,
    this.billingData,
  });

  @override
  Widget build(BuildContext context) {
    // Create unique tag to ensure fresh controller instance
    final String controllerTag = '${mode.name}_${billingData?.id ?? DateTime.now().millisecondsSinceEpoch}';

    final CreateBillingController controller = Get.put(
      CreateBillingController(
        mode: mode,
        initialBillingData: billingData,
        initialPatientName: patientName,
        initialServiceName: serviceName,
        initialAmount: amount,
      ),
      tag: controllerTag,
    );
    final PermissionService permissionService = PermissionService();
    final bool isReadOnly = mode == BillingScreenMode.view;

    String getAppBarTitle() {
      switch (mode) {
        case BillingScreenMode.create:
          return 'Create Billing';
        case BillingScreenMode.edit:
          return 'Edit Billing';
        case BillingScreenMode.view:
          return 'View Billing';
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(getAppBarTitle()),
      ),
      body: Obx(() {
        if (controller.isLoadingBillingData.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading billing details...'),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              if (showSearchOption && !isReadOnly)
                Visibility(
                  visible: permissionService.hasPermission('billing', 'is_list'),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Search Patient',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const Text(
                            ' *',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          boxShadow: AppDecoration.shadow1_3,
                          borderRadius: BorderRadiusStyle.radius8,
                        ),
                        child: CustomSearchField<PatientData>(
                          fetchItems: controller.getFilteredPatients,
                          hintText: 'Search Patient',
                          itemAsString: (patient) => patient.displayName,
                          onSelected: (patient) {
                            if (patient != null) {
                              controller.selectPatient(patient);
                            }
                          },
                          searchController: controller.searchPatientController,
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              const Text(
                'First Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.firstNameController,
                hintText: 'Enter First Name',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.lastNameController,
                hintText: 'Enter Last Name',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Patient ID',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.patientIdController,
                hintText: 'Enter Patient ID',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.ageController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.dobController,
                          hintText: '09 Feb 2021',
                          prefix: const Icon(Icons.calendar_today),
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Obx(() => Row(
                children: [
                  Radio<String>(
                    value: 'Male',
                    groupValue: controller.selectedPatient.value?.gender,
                    onChanged: isReadOnly ? null : (value) {
                      // This should be handled in the controller if editable
                      if (controller.selectedPatient.value != null) {
                        controller.selectedPatient.value = PatientData(
                          firstName: controller.selectedPatient.value!.firstName,
                          lastName: controller.selectedPatient.value!.lastName,
                          patientRegId: controller.selectedPatient.value!.patientRegId,
                          age: controller.selectedPatient.value!.age,
                          gender: value,
                          mobile: controller.selectedPatient.value!.mobile,
                          dob: controller.selectedPatient.value!.dob,
                        );
                      }
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Text('Male'),
                  Radio<String>(
                    value: 'Female',
                    groupValue: controller.selectedPatient.value?.gender,
                    onChanged: isReadOnly ? null : (value) {
                      // This should be handled in the controller if editable
                      if (controller.selectedPatient.value != null) {
                        controller.selectedPatient.value = PatientData(
                          firstName: controller.selectedPatient.value!.firstName,
                          lastName: controller.selectedPatient.value!.lastName,
                          patientRegId: controller.selectedPatient.value!.patientRegId,
                          age: controller.selectedPatient.value!.age,
                          gender: value,
                          mobile: controller.selectedPatient.value!.mobile,
                          dob: controller.selectedPatient.value!.dob,
                        );
                      }
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Text('Female'),
                ],
              )),
              const SizedBox(height: 20),
              if (!isReadOnly)
                Visibility(
                  visible: permissionService.hasPermission('billing', 'is_list'),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Search Service',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const Text(
                            ' *',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          boxShadow: AppDecoration.shadow1_3,
                          borderRadius: BorderRadiusStyle.radius8,
                        ),
                        child: CustomSearchField<ServiceModel>(
                          fetchItems: controller.getFilteredServices,
                          hintText: 'Search Service Name',
                          itemAsString: (service) => service.serviceName ?? '',
                          onSelected: (service) {
                            if (service != null) {
                              controller.selectService(service);
                            }
                          },
                          searchController: controller.searchServiceController,
                        ),
                      ),
                    const SizedBox(height: 16),
                    Obx(() {
                      if (controller.selectedServices.isEmpty) {
                        return const SizedBox.shrink();
                      }
                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.selectedServices.length,
                        itemBuilder: (context, index) {
                          final service = controller.selectedServices[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    service.displayServiceName,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Price: ${service.price.toStringAsFixed(2)}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      Row(
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.remove_circle_outline),
                                            onPressed: isReadOnly ? null : () => controller.decrementQuantity(service),
                                          ),
                                          Text(
                                            '${service.quantity}',
                                            style: const TextStyle(fontSize: 16),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.add_circle_outline),
                                            onPressed: isReadOnly ? null : () => controller.incrementQuantity(service),
                                          ),
                                          if (!isReadOnly)
                                            IconButton(
                                              icon: const Icon(Icons.delete_outline, color: Colors.red),
                                              onPressed: () => controller.removeService(service),
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    }),
                    ],
                  ),
                ),

              // Show services in view mode (read-only)
              if (isReadOnly)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Services',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Obx(() {
                      if (controller.selectedServices.isEmpty) {
                        return const Text(
                          'No services selected',
                          style: TextStyle(color: Colors.grey),
                        );
                      }
                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.selectedServices.length,
                        itemBuilder: (context, index) {
                          final service = controller.selectedServices[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    service.displayServiceName,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Price: ${service.price.toStringAsFixed(2)}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      Text(
                                        'Quantity: ${service.quantity}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      Text(
                                        'Total: ${service.totalPrice.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    }),
                  ],
                ),

              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (%)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.discountController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (Amount)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.discountAmountController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Total Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.totalAmountController,
                hintText: 'Enter Total Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Paid Amount',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.paidAmountController,
                hintText: 'Enter Paid Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Balance Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.balanceAmountController,
                hintText: 'Enter Balance Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Payment Source',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Obx(() => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    hintText: 'Select Payment Source',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide: BorderSide(color: AppColors.primary, width: 1),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  ),
                  value: controller.selectedPaymentSource.value,
                  isExpanded: true,
                  dropdownColor: Colors.white,
                  menuMaxHeight: 200,
                  elevation: 8,
                  borderRadius: BorderRadius.circular(12.0),
                  items: [
                    DropdownMenuItem(
                      value: 'UPI',
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Icon(Icons.payment, size: 20, color: AppColors.primary),
                            const SizedBox(width: 12),
                            const Text(
                              'UPI',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'Card',
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Icon(Icons.credit_card, size: 20, color: AppColors.primary),
                            const SizedBox(width: 12),
                            const Text(
                              'Card',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'Cash',
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Icon(Icons.money, size: 20, color: AppColors.primary),
                            const SizedBox(width: 12),
                            const Text(
                              'Cash',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  onChanged: isReadOnly ? null : (newValue) {
                    controller.selectedPaymentSource.value = newValue;
                  },
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600,
                  ),
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                ),
              )),
              const SizedBox(height: 30),
              if (mode != BillingScreenMode.view)
                CustomElevatedButton(
                  onPressed: controller.saveBilling,
                  text: mode == BillingScreenMode.edit ? 'Update' : 'Save',
                  buttonStyle: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                ),
              // Add extra space at the bottom to ensure dropdown has room to open below
              const SizedBox(height: 100),
              ],
            ),
          ),
        );
      }),
    );
  }
}
