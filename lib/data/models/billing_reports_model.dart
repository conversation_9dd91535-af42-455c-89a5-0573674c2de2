class BillingReportsModel {
  final List<BillingReportData> data;
  final PaginationData pagination;

  BillingReportsModel({
    required this.data,
    required this.pagination,
  });

  factory BillingReportsModel.fromJson(Map<String, dynamic> json) {
    return BillingReportsModel(
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => BillingReportData.fromJson(item))
              .toList() ??
          [],
      pagination: PaginationData.fromJson(json['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

class BillingReportData {
  final String? patientRegId;
  final String? name;
  final String? mobile;
  final String? paymentSource;
  final String? id;
  final List<BillingReportService> services;

  BillingReportData({
    this.patientRegId,
    this.name,
    this.mobile,
    this.paymentSource,
    this.id,
    this.services = const [],
  });

  factory BillingReportData.fromJson(Map<String, dynamic> json) {
    return BillingReportData(
      patientRegId: json['patient_reg_id']?.toString(),
      name: json['name']?.toString(),
      mobile: json['mobile']?.toString(),
      paymentSource: json['payment_source']?.toString(),
      id: json['id']?.toString(),
      services: (json['services'] as List<dynamic>?)
              ?.map((item) => BillingReportService.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'patient_reg_id': patientRegId,
      'name': name,
      'mobile': mobile,
      'payment_source': paymentSource,
      'id': id,
      'services': services.map((service) => service.toJson()).toList(),
    };
  }

  // Helper method to get display name with fallback
  String get displayName => name?.isNotEmpty == true ? name! : 'N/A';
  
  // Helper method to get display mobile with fallback
  String get displayMobile => mobile?.isNotEmpty == true ? mobile! : 'N/A';
  
  // Helper method to get display payment source with fallback
  String get displayPaymentSource => paymentSource?.isNotEmpty == true ? paymentSource! : 'N/A';
  
  // Helper method to get display patient reg ID with fallback
  String get displayPatientRegId => patientRegId?.isNotEmpty == true ? patientRegId! : 'N/A';

  // Helper method to get display services
  String get displayServices {
    if (services.isEmpty) return 'Services available';
    if (services.length == 1) return services.first.displayServiceName;
    if (services.length == 2) return '${services.first.displayServiceName}, ${services[1].displayServiceName}';
    return '${services.first.displayServiceName} +${services.length - 1} more';
  }
}

class BillingReportService {
  final String? serviceId;
  final String? serviceName;
  final int quantity;
  final double price;

  BillingReportService({
    this.serviceId,
    this.serviceName,
    this.quantity = 1,
    this.price = 0.0,
  });

  factory BillingReportService.fromJson(Map<String, dynamic> json) {
    return BillingReportService(
      serviceId: json['service_id']?.toString(),
      serviceName: json['servicename']?.toString() ?? json['service_name']?.toString(),
      quantity: json['quantity'] is int ? json['quantity'] : int.tryParse(json['quantity']?.toString() ?? '1') ?? 1,
      price: json['price'] is double
          ? json['price']
          : double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'service_name': serviceName,
      'quantity': quantity,
      'price': price,
    };
  }

  // Helper methods
  double get totalPrice => price * quantity;
  String get displayServiceName => serviceName ?? 'Unknown Service';
}

class PaginationData {
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  PaginationData({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
    };
  }

  // Helper methods for pagination
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}
